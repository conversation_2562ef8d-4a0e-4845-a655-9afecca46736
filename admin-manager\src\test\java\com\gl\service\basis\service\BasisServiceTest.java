package com.gl.service.basis.service;

import com.gl.framework.common.util.SecurityUtils;
import com.gl.framework.security.LoginUser;
import com.gl.framework.web.response.Result;
import com.gl.service.basis.entity.BaseAbout;
import com.gl.service.basis.entity.BaseService;
import com.gl.service.basis.repository.BaseAboutRepository;
import com.gl.service.basis.repository.BaseServiceRepository;
import com.gl.service.basis.vo.BasisVo;
import com.gl.service.music.repository.BackgroundMusicTypeRepository;
import com.gl.service.opus.entity.BackgroundMusicType;
import com.gl.service.opus.entity.TemplateType;
import com.gl.service.template.repository.TemplateTypeRepository;
import com.gl.system.vo.SysUserVo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * BasisService单元测试类
 * 测试基础设置服务的所有公共方法，包括正面和负面场景
 *
 * @author: Test
 * @date: 2024/12/19
 * @version: 1.0
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("BasisService单元测试")
@SuppressWarnings({ "unchecked", "rawtypes" })
class BasisServiceTest {

    @Mock
    private JdbcTemplate jdbcTemplate;

    @Mock
    private BaseAboutRepository baseAboutRepository;

    @Mock
    private BaseServiceRepository baseServiceRepository;

    @Mock
    private BackgroundMusicTypeRepository backgroundMusicTypeRepository;

    @Mock
    private TemplateTypeRepository templateTypeRepository;

    @InjectMocks
    private BasisService basisService;

    private BaseService mockBaseService;
    private BaseAbout mockBaseAbout;
    private List<TemplateType> mockTemplateTypes;
    private List<BackgroundMusicType> mockBackgroundMusicTypes;
    private SysUserVo mockUser;
    private LoginUser mockLoginUser;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        mockBaseService = new BaseService();
        mockBaseService.setId(1L);
        mockBaseService.setWechat("test_wechat");
        mockBaseService.setPhone("13800138000");

        mockBaseAbout = new BaseAbout();
        mockBaseAbout.setId(1L);
        mockBaseAbout.setContent("关于我们的内容");

        TemplateType templateType = new TemplateType();
        templateType.setId(1L);
        templateType.setName("测试模板类型");
        templateType.setDelStatus(0);
        mockTemplateTypes = Arrays.asList(templateType);

        BackgroundMusicType musicType = new BackgroundMusicType();
        musicType.setId(1L);
        musicType.setName("测试音乐类型");
        mockBackgroundMusicTypes = Arrays.asList(musicType);

        mockUser = new SysUserVo();
        mockUser.setId(1L);
        mockUser.setUserName("testUser");

        mockLoginUser = new LoginUser();
        mockLoginUser.setUser(mockUser);
    }

    @Test
    @DisplayName("查询基础设置信息 - 成功场景，所有数据都存在")
    void testSelect_Success_AllDataExists() {
        // Given
        when(jdbcTemplate.query(contains("dub_base_service"), any(BeanPropertyRowMapper.class)))
                .thenReturn((List) Arrays.asList(mockBaseService));
        when(jdbcTemplate.query(contains("dub_base_about"), any(BeanPropertyRowMapper.class)))
                .thenReturn((List) Arrays.asList(mockBaseAbout));
        when(jdbcTemplate.query(contains("dub_template_type"), any(BeanPropertyRowMapper.class)))
                .thenReturn((List) mockTemplateTypes);
        when(backgroundMusicTypeRepository.findAll()).thenReturn(mockBackgroundMusicTypes);

        // When
        Result result = basisService.select();

        // Then
        assertNotNull(result);
        assertEquals(200, result.getCode());
        assertNotNull(result.getData());

        BasisVo basisVo = (BasisVo) result.getData();
        assertNotNull(basisVo.getBaseService());
        assertNotNull(basisVo.getBaseAbout());
        assertNotNull(basisVo.getTemplateTypes());
        assertNotNull(basisVo.getBackgroundMusicTypes());
        assertEquals(1, basisVo.getTemplateTypes().size());
        assertEquals(1, basisVo.getBackgroundMusicTypes().size());

        // 验证方法调用
        verify(jdbcTemplate, times(3)).query(anyString(), any(BeanPropertyRowMapper.class));
        verify(backgroundMusicTypeRepository).findAll();
    }

    @Test
    @DisplayName("查询基础设置信息 - 成功场景，部分数据为空")
    void testSelect_Success_PartialDataEmpty() {
        // Given
        when(jdbcTemplate.query(contains("dub_base_service"), any(BeanPropertyRowMapper.class)))
                .thenReturn(Collections.emptyList());
        when(jdbcTemplate.query(contains("dub_base_about"), any(BeanPropertyRowMapper.class)))
                .thenReturn(Collections.emptyList());
        when(jdbcTemplate.query(contains("dub_template_type"), any(BeanPropertyRowMapper.class)))
                .thenReturn(Collections.emptyList());
        when(backgroundMusicTypeRepository.findAll()).thenReturn(Collections.emptyList());

        // When
        Result result = basisService.select();

        // Then
        assertNotNull(result);
        assertEquals(200, result.getCode());

        BasisVo basisVo = (BasisVo) result.getData();
        assertNull(basisVo.getBaseService());
        assertNull(basisVo.getBaseAbout());
        assertTrue(basisVo.getTemplateTypes().isEmpty());
        assertTrue(basisVo.getBackgroundMusicTypes().isEmpty());
    }

    @Test
    @DisplayName("查询基础设置信息 - 异常场景，数据库查询失败")
    void testSelect_Exception_DatabaseError() {
        // Given
        when(jdbcTemplate.query(contains("dub_base_service"), any(BeanPropertyRowMapper.class)))
                .thenThrow(new RuntimeException("数据库连接失败"));

        // When & Then
        assertThrows(RuntimeException.class, () -> basisService.select());

        verify(jdbcTemplate).query(contains("dub_base_service"), any(BeanPropertyRowMapper.class));
    }

    @Test
    @DisplayName("添加基础设置信息 - 成功场景，包含所有数据")
    void testAdd_Success_AllDataProvided() {
        // Given
        BasisVo basisVo = new BasisVo();
        basisVo.setBaseService(mockBaseService);
        basisVo.setBaseAbout(mockBaseAbout);
        basisVo.setTemplateTypes(mockTemplateTypes);
        basisVo.setBackgroundMusicTypes(mockBackgroundMusicTypes);

        try (MockedStatic<SecurityUtils> mockedSecurityUtils = mockStatic(SecurityUtils.class)) {
            mockedSecurityUtils.when(SecurityUtils::getLoginUser).thenReturn(mockLoginUser);

            when(baseServiceRepository.save(any(BaseService.class))).thenReturn(mockBaseService);
            when(baseAboutRepository.save(any(BaseAbout.class))).thenReturn(mockBaseAbout);
            when(templateTypeRepository.saveAll(anyList())).thenReturn(mockTemplateTypes);
            when(backgroundMusicTypeRepository.saveAll(anyList())).thenReturn(mockBackgroundMusicTypes);

            // When
            Result result = basisService.add(basisVo);

            // Then
            assertNotNull(result);
            assertEquals(200, result.getCode());
            assertEquals("success", result.getMessage());

            verify(baseServiceRepository).save(mockBaseService);
            verify(baseAboutRepository).save(mockBaseAbout);
            verify(templateTypeRepository).saveAll(anyList());
            verify(backgroundMusicTypeRepository).saveAll(mockBackgroundMusicTypes);
        }
    }

    @Test
    @DisplayName("添加基础设置信息 - 成功场景，新增模板类型")
    void testAdd_Success_NewTemplateType() {
        // Given
        TemplateType newTemplateType = new TemplateType();
        newTemplateType.setName("新模板类型");
        // 没有设置ID，表示新增

        BasisVo basisVo = new BasisVo();
        basisVo.setTemplateTypes(Arrays.asList(newTemplateType));
        basisVo.setBackgroundMusicTypes(Collections.emptyList());

        try (MockedStatic<SecurityUtils> mockedSecurityUtils = mockStatic(SecurityUtils.class)) {
            mockedSecurityUtils.when(SecurityUtils::getLoginUser).thenReturn(mockLoginUser);

            when(templateTypeRepository.saveAll(anyList())).thenReturn(Arrays.asList(newTemplateType));

            // When
            Result result = basisService.add(basisVo);

            // Then
            assertNotNull(result);
            assertEquals(200, result.getCode());

            verify(templateTypeRepository).saveAll(argThat(list -> {
                List<TemplateType> templateList = (List<TemplateType>) list;
                TemplateType savedType = templateList.get(0);
                return savedType.getDelStatus() == 0 &&
                        savedType.getCreateId().equals(1L) &&
                        savedType.getCreateTime() != null;
            }));
        }
    }

    @Test
    @DisplayName("添加基础设置信息 - 成功场景，更新现有模板类型")
    void testAdd_Success_UpdateExistingTemplateType() {
        // Given
        TemplateType existingTemplateType = new TemplateType();
        existingTemplateType.setId(1L);
        existingTemplateType.setName("更新后的模板类型");

        TemplateType dbTemplateType = new TemplateType();
        dbTemplateType.setId(1L);
        dbTemplateType.setName("原始模板类型");
        dbTemplateType.setDelStatus(0);
        dbTemplateType.setCreateId(1L);
        dbTemplateType.setCreateTime(new Date());

        BasisVo basisVo = new BasisVo();
        basisVo.setTemplateTypes(Arrays.asList(existingTemplateType));
        basisVo.setBackgroundMusicTypes(Collections.emptyList());

        try (MockedStatic<SecurityUtils> mockedSecurityUtils = mockStatic(SecurityUtils.class)) {
            mockedSecurityUtils.when(SecurityUtils::getLoginUser).thenReturn(mockLoginUser);

            when(templateTypeRepository.findById(1L)).thenReturn(Optional.of(dbTemplateType));
            when(templateTypeRepository.saveAll(anyList())).thenReturn(Arrays.asList(dbTemplateType));

            // When
            Result result = basisService.add(basisVo);

            // Then
            assertNotNull(result);
            assertEquals(200, result.getCode());

            verify(templateTypeRepository).findById(1L);
            verify(templateTypeRepository).saveAll(argThat(list -> {
                List<TemplateType> templateList = (List<TemplateType>) list;
                TemplateType savedType = templateList.get(0);
                return "更新后的模板类型".equals(savedType.getName());
            }));
        }
    }

    @Test
    @DisplayName("添加基础设置信息 - 成功场景，部分数据为null")
    void testAdd_Success_PartialDataNull() {
        // Given
        BasisVo basisVo = new BasisVo();
        basisVo.setBaseService(null);
        basisVo.setBaseAbout(null);
        basisVo.setTemplateTypes(Collections.emptyList());
        basisVo.setBackgroundMusicTypes(Collections.emptyList());

        try (MockedStatic<SecurityUtils> mockedSecurityUtils = mockStatic(SecurityUtils.class)) {
            mockedSecurityUtils.when(SecurityUtils::getLoginUser).thenReturn(mockLoginUser);

            // When
            Result result = basisService.add(basisVo);

            // Then
            assertNotNull(result);
            assertEquals(200, result.getCode());

            // 验证没有调用保存方法
            verify(baseServiceRepository, never()).save(any());
            verify(baseAboutRepository, never()).save(any());
            verify(templateTypeRepository, never()).saveAll(any());
            verify(backgroundMusicTypeRepository, never()).saveAll(any());
        }
    }

    @Test
    @DisplayName("删除模板类型 - 成功场景")
    void testDeleteTemplate_Success() {
        // Given
        Long templateTypeId = 1L;
        when(templateTypeRepository.updateDelStatusById(templateTypeId)).thenReturn(1);

        // When
        Result result = basisService.deleteTemplate(templateTypeId);

        // Then
        assertNotNull(result);
        assertEquals(200, result.getCode());
        assertEquals("success", result.getMessage());

        verify(templateTypeRepository).updateDelStatusById(templateTypeId);
    }

    @Test
    @DisplayName("删除模板类型 - 失败场景，ID为null")
    void testDeleteTemplate_Fail_NullId() {
        // When
        Result result = basisService.deleteTemplate(null);

        // Then
        assertNotNull(result);
        assertEquals(500, result.getCode());
        assertEquals("模板类型id不能为空", result.getMessage());

        verify(templateTypeRepository, never()).updateDelStatusById(any());
    }

    @Test
    @DisplayName("删除背景音乐类型 - 成功场景")
    void testDeleteMusic_Success() {
        // Given
        Long musicTypeId = 1L;
        doNothing().when(backgroundMusicTypeRepository).deleteById(musicTypeId);

        // When
        Result result = basisService.deleteMusic(musicTypeId);

        // Then
        assertNotNull(result);
        assertEquals(200, result.getCode());
        assertEquals("success", result.getMessage());

        verify(backgroundMusicTypeRepository).deleteById(musicTypeId);
    }

    @Test
    @DisplayName("删除背景音乐类型 - 失败场景，ID为null")
    void testDeleteMusic_Fail_NullId() {
        // When
        Result result = basisService.deleteMusic(null);

        // Then
        assertNotNull(result);
        assertEquals(500, result.getCode());
        assertEquals("背景音乐类型不能为空", result.getMessage());

        verify(backgroundMusicTypeRepository, never()).deleteById(any());
    }

    @Test
    @DisplayName("删除背景音乐类型 - 异常场景，数据库操作失败")
    void testDeleteMusic_Exception_DatabaseError() {
        // Given
        Long musicTypeId = 1L;
        doThrow(new RuntimeException("数据库操作失败")).when(backgroundMusicTypeRepository).deleteById(musicTypeId);

        // When & Then
        assertThrows(RuntimeException.class, () -> basisService.deleteMusic(musicTypeId));

        verify(backgroundMusicTypeRepository).deleteById(musicTypeId);
    }

    @Test
    @DisplayName("添加基础设置信息 - 异常场景，获取用户信息失败")
    void testAdd_Exception_SecurityUtilsError() {
        // Given
        BasisVo basisVo = new BasisVo();
        basisVo.setBaseService(mockBaseService);

        try (MockedStatic<SecurityUtils> mockedSecurityUtils = mockStatic(SecurityUtils.class)) {
            mockedSecurityUtils.when(SecurityUtils::getLoginUser)
                    .thenThrow(new RuntimeException("获取用户信息失败"));

            // When & Then
            assertThrows(RuntimeException.class, () -> basisService.add(basisVo));

            // 验证没有调用保存方法
            verify(baseServiceRepository, never()).save(any());
        }
    }

    @Test
    @DisplayName("添加基础设置信息 - 异常场景，模板类型不存在")
    void testAdd_Exception_TemplateTypeNotFound() {
        // Given
        TemplateType existingTemplateType = new TemplateType();
        existingTemplateType.setId(999L); // 不存在的ID
        existingTemplateType.setName("不存在的模板类型");

        BasisVo basisVo = new BasisVo();
        basisVo.setTemplateTypes(Arrays.asList(existingTemplateType));
        basisVo.setBackgroundMusicTypes(Collections.emptyList());

        try (MockedStatic<SecurityUtils> mockedSecurityUtils = mockStatic(SecurityUtils.class)) {
            mockedSecurityUtils.when(SecurityUtils::getLoginUser).thenReturn(mockLoginUser);

            when(templateTypeRepository.findById(999L)).thenReturn(Optional.empty());
            when(templateTypeRepository.saveAll(anyList())).thenReturn(Arrays.asList(existingTemplateType));

            // When
            Result result = basisService.add(basisVo);

            // Then
            assertNotNull(result);
            assertEquals(200, result.getCode());

            verify(templateTypeRepository).findById(999L);
            verify(templateTypeRepository).saveAll(argThat(list -> {
                List<TemplateType> templateList = (List<TemplateType>) list;
                TemplateType savedType = templateList.get(0);
                return savedType.getId().equals(999L) && "不存在的模板类型".equals(savedType.getName());
            }));
        }
    }

    @Test
    @DisplayName("添加基础设置信息 - 异常场景，Repository保存失败")
    void testAdd_Exception_RepositorySaveError() {
        // Given
        BasisVo basisVo = new BasisVo();
        basisVo.setBaseService(mockBaseService);

        try (MockedStatic<SecurityUtils> mockedSecurityUtils = mockStatic(SecurityUtils.class)) {
            mockedSecurityUtils.when(SecurityUtils::getLoginUser).thenReturn(mockLoginUser);

            when(baseServiceRepository.save(any(BaseService.class)))
                    .thenThrow(new RuntimeException("数据库保存失败"));

            // When & Then
            assertThrows(RuntimeException.class, () -> basisService.add(basisVo));

            verify(baseServiceRepository).save(mockBaseService);
        }
    }

    @Test
    @DisplayName("删除模板类型 - 异常场景，数据库更新失败")
    void testDeleteTemplate_Exception_DatabaseUpdateError() {
        // Given
        Long templateTypeId = 1L;
        when(templateTypeRepository.updateDelStatusById(templateTypeId))
                .thenThrow(new RuntimeException("数据库更新失败"));

        // When & Then
        assertThrows(RuntimeException.class, () -> basisService.deleteTemplate(templateTypeId));

        verify(templateTypeRepository).updateDelStatusById(templateTypeId);
    }

    @Test
    @DisplayName("查询基础设置信息 - 边界场景，背景音乐类型Repository返回null")
    void testSelect_Edge_BackgroundMusicTypeRepositoryReturnsNull() {
        // Given
        when(jdbcTemplate.query(contains("dub_base_service"), any(BeanPropertyRowMapper.class)))
                .thenReturn(Arrays.asList(mockBaseService));
        when(jdbcTemplate.query(contains("dub_base_about"), any(BeanPropertyRowMapper.class)))
                .thenReturn(Arrays.asList(mockBaseAbout));
        when(jdbcTemplate.query(contains("dub_template_type"), any(BeanPropertyRowMapper.class)))
                .thenReturn(mockTemplateTypes);
        when(backgroundMusicTypeRepository.findAll()).thenReturn(null);

        // When
        Result result = basisService.select();

        // Then
        assertNotNull(result);
        assertEquals(200, result.getCode());

        BasisVo basisVo = (BasisVo) result.getData();
        assertNull(basisVo.getBackgroundMusicTypes());
    }

    @Test
    @DisplayName("添加基础设置信息 - 边界场景，模板类型列表包含null元素")
    void testAdd_Edge_TemplateTypeListContainsNull() {
        // Given
        List<TemplateType> templateTypesWithNull = new ArrayList<>();
        templateTypesWithNull.add(mockTemplateTypes.get(0));
        templateTypesWithNull.add(null); // 添加null元素

        BasisVo basisVo = new BasisVo();
        basisVo.setTemplateTypes(templateTypesWithNull);
        basisVo.setBackgroundMusicTypes(Collections.emptyList());

        try (MockedStatic<SecurityUtils> mockedSecurityUtils = mockStatic(SecurityUtils.class)) {
            mockedSecurityUtils.when(SecurityUtils::getLoginUser).thenReturn(mockLoginUser);

            when(templateTypeRepository.saveAll(anyList())).thenReturn(mockTemplateTypes);

            // When & Then
            // 这应该抛出NullPointerException，因为代码没有处理null元素
            assertThrows(NullPointerException.class, () -> basisService.add(basisVo));
        }
    }

    @Test
    @DisplayName("添加基础设置信息 - 数据库性能测试，大数据量模板类型数据")
    void testAdd_Performance_LargeTemplateTypeList() {
        // Given - 创建大量数据用于性能测试
        List<TemplateType> largeTemplateTypeList = new ArrayList<>();
        for (int i = 0; i < 10000; i++) { // 增加到10000条数据
            TemplateType templateType = new TemplateType();
            templateType.setName("性能测试模板类型_" + i + "_" + System.currentTimeMillis());
            largeTemplateTypeList.add(templateType);
        }

        BasisVo basisVo = new BasisVo();
        basisVo.setTemplateTypes(largeTemplateTypeList);
        basisVo.setBackgroundMusicTypes(Collections.emptyList());

        try (MockedStatic<SecurityUtils> mockedSecurityUtils = mockStatic(SecurityUtils.class)) {
            mockedSecurityUtils.when(SecurityUtils::getLoginUser).thenReturn(mockLoginUser);

            when(templateTypeRepository.saveAll(anyList())).thenReturn(largeTemplateTypeList);

            // When
            long startTime = System.currentTimeMillis();
            Result result = basisService.add(basisVo);
            long endTime = System.currentTimeMillis();

            // Then
            assertNotNull(result);
            assertEquals(200, result.getCode());

            // 验证数据库性能（处理大量数据的耗时测试，设置为30秒）
            long executionTime = endTime - startTime;
            System.out.println("数据库性能测试 - 处理10000条模板类型数据耗时: " + executionTime + "ms");
            assertTrue(executionTime < 30000, "处理10000个模板类型数据应该在30秒内完成，实际耗时: " + executionTime + "ms");

            // 验证性能基准：如果耗时超过10秒，输出警告
            if (executionTime > 10000) {
                System.out.println("警告：数据库性能可能需要优化，当前耗时: " + executionTime + "ms");
            }

            verify(templateTypeRepository).saveAll(argThat(list -> {
                List<TemplateType> templateList = (List<TemplateType>) list;
                return templateList.size() == 10000;
            }));
        }
    }
}

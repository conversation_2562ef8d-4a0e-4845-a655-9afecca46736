package com.gl.service.device.controller;

import com.gl.framework.web.response.Result;
import com.gl.service.device.service.DeviceService;
import com.gl.service.device.vo.DeviceVo;
import com.gl.service.device.vo.dto.DeviceAddWorkDto;
import com.gl.service.device.vo.dto.DeviceDto;
import com.gl.service.device.vo.dto.DeviceUpdateVolume;
import com.gl.service.device.vo.dto.DeviceVoiceDto;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * DeviceController 简化单元测试类
 * 直接测试Controller方法，不依赖Spring MVC
 * 
 * @author: test
 * @date: 2025-01-11
 * @version: 1.0
 */
@ExtendWith(MockitoExtension.class)
class DeviceControllerSimpleTest {

    @InjectMocks
    private DeviceController deviceController;

    @Mock
    private DeviceService deviceService;

    @Mock
    private HttpServletResponse response;

    private DeviceDto deviceDto;
    private DeviceVo deviceVo;
    private DeviceAddWorkDto deviceAddWorkDto;
    private DeviceUpdateVolume deviceUpdateVolume;
    private DeviceVoiceDto deviceVoiceDto;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        deviceDto = new DeviceDto();
        deviceDto.setDeviceId(1L);
        deviceDto.setStatus(1);
        deviceDto.setBindStatus(1);
        deviceDto.setUseStatus(1);
        deviceDto.setSearchCondition("test");
        deviceDto.setIds(Arrays.asList(1L, 2L));
        deviceDto.setShopId(1L);

        deviceVo = new DeviceVo();
        deviceVo.setId(1L);
        deviceVo.setName("测试设备");
        deviceVo.setSn("TEST001");
        deviceVo.setVolume(50);
        deviceVo.setShopId(1L);
        deviceVo.setUserId(1L);
        deviceVo.setBindStatus(1);
        deviceVo.setUseStatus(1);

        deviceAddWorkDto = new DeviceAddWorkDto();
        deviceAddWorkDto.setId(1L);
        deviceAddWorkDto.setDeviceId(1L);
        deviceAddWorkDto.setWorkId(1L);

        deviceUpdateVolume = new DeviceUpdateVolume();
        ArrayList<Long> deviceIds = new ArrayList<>();
        deviceIds.add(1L);
        deviceIds.add(2L);
        deviceUpdateVolume.setDeviceIdList(deviceIds);
        deviceUpdateVolume.setVolume(80);

        deviceVoiceDto = new DeviceVoiceDto();
        deviceVoiceDto.setId(1L);
        deviceVoiceDto.setSortBy(5);
    }

    /**
     * 测试设备列表查询
     */
    @Test
    @DisplayName("测试设备列表查询")
    void testList_Success() {
        // Given
        Result mockResult = Result.success();
        when(deviceService.list(any(DeviceDto.class), eq(1))).thenReturn(mockResult);

        // When
        Result result = deviceController.list(deviceDto);

        // Then
        assertNotNull(result);
        assertEquals(200, result.getCode());
        assertEquals("success", result.getMessage());
        verify(deviceService, times(1)).list(deviceDto, 1);
    }

    /**
     * 测试导出设备列表
     */
    @Test
    @DisplayName("测试导出设备列表")
    void testExportList_Success() throws IOException {
        // Given
        doNothing().when(deviceService).exportList(any(DeviceDto.class), any(HttpServletResponse.class));

        // When
        deviceController.exportList(deviceDto, response);

        // Then
        verify(deviceService, times(1)).exportList(deviceDto, response);
    }

    /**
     * 测试新增设备
     */
    @Test
    @DisplayName("测试新增设备")
    void testAddOrUpdate_Add_Success() {
        // Given
        deviceVo.setId(null); // 新增场景
        Result mockResult = Result.success();
        when(deviceService.add(any(DeviceVo.class))).thenReturn(mockResult);

        // When
        Result result = deviceController.addOrUpdate(deviceVo);

        // Then
        assertNotNull(result);
        assertEquals(200, result.getCode());
        assertEquals("success", result.getMessage());
        verify(deviceService, times(1)).add(deviceVo);
        verify(deviceService, never()).update(any(DeviceVo.class));
    }

    /**
     * 测试修改设备
     */
    @Test
    @DisplayName("测试修改设备")
    void testAddOrUpdate_Update_Success() {
        // Given
        Result mockResult = Result.success();
        when(deviceService.update(any(DeviceVo.class))).thenReturn(mockResult);

        // When
        Result result = deviceController.addOrUpdate(deviceVo);

        // Then
        assertNotNull(result);
        assertEquals(200, result.getCode());
        assertEquals("success", result.getMessage());
        verify(deviceService, times(1)).update(deviceVo);
        verify(deviceService, never()).add(any(DeviceVo.class));
    }

    /**
     * 测试绑定与解绑设备
     */
    @Test
    void testBindAndUntie_Success() {
        // Given
        Result mockResult = Result.success();
        when(deviceService.bindAndUntie(any(DeviceVo.class))).thenReturn(mockResult);

        // When
        Result result = deviceController.bindAndUntie(deviceVo);

        // Then
        assertNotNull(result);
        assertEquals(200, result.getCode());
        assertEquals("success", result.getMessage());
        verify(deviceService, times(1)).bindAndUntie(deviceVo);
    }

    /**
     * 测试修改使用状态
     */
    @Test
    void testUpdateUseStatus_Success() {
        // Given
        Result mockResult = Result.success();
        when(deviceService.updateUseStatus(any(DeviceDto.class))).thenReturn(mockResult);

        // When
        Result result = deviceController.updateUseStatus(deviceDto);

        // Then
        assertNotNull(result);
        assertEquals(200, result.getCode());
        assertEquals("success", result.getMessage());
        verify(deviceService, times(1)).updateUseStatus(deviceDto);
    }

    /**
     * 测试删除设备
     */
    @Test
    void testDelete_Success() {
        // Given
        Result mockResult = Result.success();
        when(deviceService.delete(any(DeviceDto.class))).thenReturn(mockResult);

        // When
        Result result = deviceController.delete(deviceDto);

        // Then
        assertNotNull(result);
        assertEquals(200, result.getCode());
        assertEquals("success", result.getMessage());
        verify(deviceService, times(1)).delete(deviceDto);
    }

    /**
     * 测试设备详情
     */
    @Test
    void testDetail_Success() {
        // Given
        Result mockResult = Result.success();
        when(deviceService.detail(1L)).thenReturn(mockResult);

        // When
        Result result = deviceController.detail(1L);

        // Then
        assertNotNull(result);
        assertEquals(200, result.getCode());
        assertEquals("success", result.getMessage());
        verify(deviceService, times(1)).detail(1L);
    }

    /**
     * 测试删除设备与语音包关系
     */
    @Test
    void testDeleteDeviceAndVoice_Success() {
        // Given
        Result mockResult = Result.success();
        when(deviceService.deleteDeviceAndVoice(any(DeviceVoiceDto.class))).thenReturn(mockResult);

        // When
        Result result = deviceController.deleteDeviceAndVoice(deviceVoiceDto);

        // Then
        assertNotNull(result);
        assertEquals(200, result.getCode());
        assertEquals("success", result.getMessage());
        verify(deviceService, times(1)).deleteDeviceAndVoice(deviceVoiceDto);
    }

    /**
     * 测试修改排序
     */
    @Test
    void testUpdateSortBy_Success() {
        // Given
        Result mockResult = Result.success();
        when(deviceService.updateSortBy(any(DeviceVoiceDto.class))).thenReturn(mockResult);

        // When
        Result result = deviceController.updateSortBy(deviceVoiceDto);

        // Then
        assertNotNull(result);
        assertEquals(200, result.getCode());
        assertEquals("success", result.getMessage());
        verify(deviceService, times(1)).updateSortBy(deviceVoiceDto);
    }

    /**
     * 测试下拉框获取设备
     */
    @Test
    void testGetDeviceList_Success() {
        // Given
        Result mockResult = Result.success();
        when(deviceService.getDeviceList()).thenReturn(mockResult);

        // When
        Result result = deviceController.getDeviceList();

        // Then
        assertNotNull(result);
        assertEquals(200, result.getCode());
        assertEquals("success", result.getMessage());
        verify(deviceService, times(1)).getDeviceList();
    }

    /**
     * 测试解绑门店
     */
    @Test
    void testRelieveShop_Success() {
        // Given
        Result mockResult = Result.success();
        when(deviceService.relieveShop(any(DeviceVo.class))).thenReturn(mockResult);

        // When
        Result result = deviceController.relieveShop(deviceVo);

        // Then
        assertNotNull(result);
        assertEquals(200, result.getCode());
        assertEquals("success", result.getMessage());
        verify(deviceService, times(1)).relieveShop(deviceVo);
    }

    /**
     * 测试设备添加作品
     */
    @Test
    void testAddWork_Success() {
        // Given
        Result mockResult = Result.success();
        when(deviceService.addWork(any(DeviceAddWorkDto.class))).thenReturn(mockResult);

        // When
        Result result = deviceController.addWork(deviceAddWorkDto);

        // Then
        assertNotNull(result);
        assertEquals(200, result.getCode());
        assertEquals("success", result.getMessage());
        verify(deviceService, times(1)).addWork(deviceAddWorkDto);
    }

    /**
     * 测试删除设备作品
     */
    @Test
    void testDelWork_Success() {
        // Given
        Result mockResult = Result.success();
        when(deviceService.delWork(any(DeviceAddWorkDto.class))).thenReturn(mockResult);

        // When
        Result result = deviceController.delWork(deviceAddWorkDto);

        // Then
        assertNotNull(result);
        assertEquals(200, result.getCode());
        assertEquals("success", result.getMessage());
        verify(deviceService, times(1)).delWork(deviceAddWorkDto);
    }

    /**
     * 测试批量修改音量
     */
    @Test
    void testUpdateVolume_Success() {
        // Given
        Result mockResult = Result.success();
        when(deviceService.updateVolume(any(DeviceUpdateVolume.class))).thenReturn(mockResult);

        // When
        Result result = deviceController.updateVolume(deviceUpdateVolume);

        // Then
        assertNotNull(result);
        assertEquals(200, result.getCode());
        assertEquals("success", result.getMessage());
        verify(deviceService, times(1)).updateVolume(deviceUpdateVolume);
    }

    /**
     * 测试用户可选设备
     */
    @Test
    void testGetUserDeviceSelect_Success() {
        // Given
        Result mockResult = Result.success();
        when(deviceService.getUserDeviceSelect()).thenReturn(mockResult);

        // When
        Result result = deviceController.getUserDeviceSelect();

        // Then
        assertNotNull(result);
        assertEquals(200, result.getCode());
        assertEquals("success", result.getMessage());
        verify(deviceService, times(1)).getUserDeviceSelect();
    }

    /**
     * 测试树形用户可选设备
     */
    @Test
    void testGetTreeUserDeviceSelect_Success() {
        // Given
        Result mockResult = Result.success();
        when(deviceService.getTreeUserDeviceSelect()).thenReturn(mockResult);

        // When
        Result result = deviceController.getTreeUserDeviceSelect();

        // Then
        assertNotNull(result);
        assertEquals(200, result.getCode());
        assertEquals("success", result.getMessage());
        verify(deviceService, times(1)).getTreeUserDeviceSelect();
    }
}

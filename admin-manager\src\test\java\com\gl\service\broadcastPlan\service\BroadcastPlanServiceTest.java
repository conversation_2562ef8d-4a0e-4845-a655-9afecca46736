package com.gl.service.broadcastPlan.service;

import cn.hutool.core.collection.CollUtil;
import com.gl.commons.enums.UserTypeEnum;
import com.gl.framework.common.util.SecurityUtils;
import com.gl.framework.security.LoginUser;
import com.gl.framework.web.response.Result;
import com.gl.service.broadcastPlan.entity.BroadcastPlan;
import com.gl.service.broadcastPlan.entity.BroadcastPlanVoiceWorkRef;
import com.gl.service.broadcastPlan.repository.BroadcastPlanRepository;
import com.gl.service.broadcastPlan.repository.BroadcastPlanVoiceWorkRepository;
import com.gl.service.broadcastPlan.vo.BroadcastPlanInfoVo;
import com.gl.service.broadcastPlan.vo.BroadcastPlanVo;
import com.gl.service.broadcastPlan.vo.dto.BroadcastPlanAddDto;
import com.gl.service.broadcastPlan.vo.dto.BroadcastPlanDto;
import com.gl.system.vo.SysUserVo;
import com.gl.util.GetShopRefUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * BroadcastPlanService单元测试类
 * 测试播报计划服务的所有公共方法，包括正面和负面场景
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("播报计划服务测试")
class BroadcastPlanServiceTest {

    @Mock
    private BroadcastPlanRepository broadcastPlanRepository;

    @Mock
    private BroadcastPlanVoiceWorkRepository broadcastPlanVoiceWorkRepository;

    @Mock
    private JdbcTemplate jdbcTemplate;

    @Mock
    private GetShopRefUtil shopRefUtil;

    @InjectMocks
    private BroadcastPlanService broadcastPlanService;

    private LoginUser mockLoginUser;
    private SysUserVo mockUser;

    @BeforeEach
    void setUp() {
        // 设置模拟用户
        mockUser = new SysUserVo();
        mockUser.setId(1L);
        mockUser.setLoginName("testUser");
        mockUser.setUserName("测试用户");
        mockUser.setType(UserTypeEnum.PLATFORM.getType());

        mockLoginUser = new LoginUser();
        mockLoginUser.setUser(mockUser);
    }

    @Test
    @DisplayName("测试list方法 - 微信用户需要过滤时返回空结果")
    void testList_WhenNeedWxFilter_ShouldReturnEmptyResult() {
        // Given
        BroadcastPlanDto dto = new BroadcastPlanDto();
        when(shopRefUtil.isNeedWxFilter()).thenReturn(true);

        // When
        Result result = broadcastPlanService.list(dto, 1);

        // Then
        assertNotNull(result);
        assertEquals(10000, result.getCode()); // SUCCESS code
        Map<String, Object> data = (Map<String, Object>) result.getData();
        assertEquals(0, data.get("total"));
        assertEquals(new ArrayList<>(), data.get("result"));
    }

    @Test
    @DisplayName("测试list方法 - 正常查询带搜索条件")
    void testList_WithSearchCondition_ShouldReturnFilteredResults() {
        // Given
        BroadcastPlanDto dto = new BroadcastPlanDto();
        dto.setSearchCondition("test");
        dto.setPageSize(10);
        dto.setPageNumber(0);

        when(shopRefUtil.isNeedWxFilter()).thenReturn(false);
        when(shopRefUtil.getShopRef()).thenReturn(Arrays.asList(1L, 2L));
        when(jdbcTemplate.queryForObject(anyString(), eq(Long.class), any(Object[].class)))
                .thenReturn(5L);

        List<BroadcastPlanVo> mockResults = createMockBroadcastPlanVoList();
        when(jdbcTemplate.query(anyString(), any(BeanPropertyRowMapper.class), any(Object[].class)))
                .thenReturn(mockResults);

        // When
        Result result = broadcastPlanService.list(dto, 1);

        // Then
        assertNotNull(result);
        assertEquals(10000, result.getCode());
        Map<String, Object> data = (Map<String, Object>) result.getData();
        assertEquals(5L, data.get("total"));
        assertEquals(mockResults, data.get("result"));
    }

    @Test
    @DisplayName("测试list方法 - 查询结果为空")
    void testList_WhenNoResults_ShouldReturnEmptyData() {
        // Given
        BroadcastPlanDto dto = new BroadcastPlanDto();
        when(shopRefUtil.isNeedWxFilter()).thenReturn(false);
        when(shopRefUtil.getShopRef()).thenReturn(Arrays.asList(1L, 2L));
        when(jdbcTemplate.queryForObject(anyString(), eq(Long.class), any(Object[].class)))
                .thenReturn(0L);

        // When
        Result result = broadcastPlanService.list(dto, 1);

        // Then
        assertNotNull(result);
        assertEquals(10000, result.getCode());
        Map<String, Object> data = (Map<String, Object>) result.getData();
        assertEquals(0, data.get("total"));
        assertNull(data.get("result"));
    }

    @Test
    @DisplayName("测试list方法 - 导出模式不分页")
    void testList_ExportMode_ShouldNotPaginate() {
        // Given
        BroadcastPlanDto dto = new BroadcastPlanDto();
        when(shopRefUtil.isNeedWxFilter()).thenReturn(false);
        when(shopRefUtil.getShopRef()).thenReturn(Arrays.asList(1L));
        when(jdbcTemplate.queryForObject(anyString(), eq(Long.class), any(Object[].class)))
                .thenReturn(3L);

        List<BroadcastPlanVo> mockResults = createMockBroadcastPlanVoList();
        when(jdbcTemplate.query(anyString(), any(BeanPropertyRowMapper.class), any(Object[].class)))
                .thenReturn(mockResults);

        // When
        Result result = broadcastPlanService.list(dto, 0); // exportType = 0 表示导出模式

        // Then
        assertNotNull(result);
        assertEquals(10000, result.getCode());
        verify(jdbcTemplate, times(1)).query(
                argThat(sql -> !sql.contains("LIMIT")),
                any(BeanPropertyRowMapper.class),
                any(Object[].class));
    }

    @Test
    @DisplayName("测试addOrUpdate方法 - 新增播报计划成功")
    void testAddOrUpdate_CreateNew_ShouldSucceed() {
        // Given
        BroadcastPlanAddDto dto = createValidBroadcastPlanAddDto();
        dto.setId(null); // 新增

        BroadcastPlan savedPlan = new BroadcastPlan();
        savedPlan.setId(1L);

        try (MockedStatic<SecurityUtils> mockedSecurityUtils = mockStatic(SecurityUtils.class)) {
            mockedSecurityUtils.when(SecurityUtils::getLoginUser).thenReturn(mockLoginUser);
            when(broadcastPlanRepository.save(any(BroadcastPlan.class))).thenReturn(savedPlan);

            // When
            Result result = broadcastPlanService.addOrUpdate(dto);

            // Then
            assertNotNull(result);
            assertEquals(10000, result.getCode());
            verify(broadcastPlanRepository, times(1)).save(any(BroadcastPlan.class));
            verify(broadcastPlanVoiceWorkRepository, times(1)).saveAll(anyList());
        }
    }

    @Test
    @DisplayName("测试addOrUpdate方法 - 更新现有播报计划成功")
    void testAddOrUpdate_UpdateExisting_ShouldSucceed() {
        // Given
        BroadcastPlanAddDto dto = createValidBroadcastPlanAddDto();
        dto.setId(1L); // 更新

        BroadcastPlan existingPlan = new BroadcastPlan();
        existingPlan.setId(1L);

        try (MockedStatic<SecurityUtils> mockedSecurityUtils = mockStatic(SecurityUtils.class)) {
            mockedSecurityUtils.when(SecurityUtils::getLoginUser).thenReturn(mockLoginUser);
            when(broadcastPlanRepository.findById(1L)).thenReturn(Optional.of(existingPlan));
            when(broadcastPlanRepository.save(any(BroadcastPlan.class))).thenReturn(existingPlan);

            // When
            Result result = broadcastPlanService.addOrUpdate(dto);

            // Then
            assertNotNull(result);
            assertEquals(10000, result.getCode());
            verify(broadcastPlanRepository, times(1)).save(any(BroadcastPlan.class));
            verify(broadcastPlanVoiceWorkRepository, times(1)).deleteByPlanId(1L);
            verify(broadcastPlanVoiceWorkRepository, times(1)).saveAll(anyList());
        }
    }

    @Test
    @DisplayName("测试addOrUpdate方法 - 参数为空时返回失败")
    void testAddOrUpdate_NullDto_ShouldReturnFailure() {
        // When
        Result result = broadcastPlanService.addOrUpdate(null);

        // Then
        assertNotNull(result);
        assertEquals(10001, result.getCode()); // FAIL code
        assertEquals("数据不能为空", result.getMessage());
    }

    @Test
    @DisplayName("测试addOrUpdate方法 - 开始时间为空时返回失败")
    void testAddOrUpdate_EmptyStartTime_ShouldReturnFailure() {
        // Given
        BroadcastPlanAddDto dto = createValidBroadcastPlanAddDto();
        dto.setStartTime("");

        // When
        Result result = broadcastPlanService.addOrUpdate(dto);

        // Then
        assertNotNull(result);
        assertEquals(10001, result.getCode());
        assertEquals("播报时段不能为空", result.getMessage());
    }

    @Test
    @DisplayName("测试addOrUpdate方法 - 结束时间为空时返回失败")
    void testAddOrUpdate_EmptyEndTime_ShouldReturnFailure() {
        // Given
        BroadcastPlanAddDto dto = createValidBroadcastPlanAddDto();
        dto.setEndTime(null);

        // When
        Result result = broadcastPlanService.addOrUpdate(dto);

        // Then
        assertNotNull(result);
        assertEquals(10001, result.getCode());
        assertEquals("播报时段不能为空", result.getMessage());
    }

    @Test
    @DisplayName("测试addOrUpdate方法 - 设备ID为空时返回失败")
    void testAddOrUpdate_NullDeviceIds_ShouldReturnFailure() {
        // Given
        BroadcastPlanAddDto dto = createValidBroadcastPlanAddDto();
        dto.setDeviceIds(null);

        // When
        Result result = broadcastPlanService.addOrUpdate(dto);

        // Then
        assertNotNull(result);
        assertEquals(10001, result.getCode());
        assertEquals("关联设备不能为空", result.getMessage());
    }

    @Test
    @DisplayName("测试addOrUpdate方法 - 语音作品为空时返回失败")
    void testAddOrUpdate_EmptyVoiceWorkList_ShouldReturnFailure() {
        // Given
        BroadcastPlanAddDto dto = createValidBroadcastPlanAddDto();
        dto.setVoiceWorkList(new ArrayList<>());

        // When
        Result result = broadcastPlanService.addOrUpdate(dto);

        // Then
        assertNotNull(result);
        assertEquals(10001, result.getCode());
        assertEquals("关联语音作品不能为空", result.getMessage());
    }

    @Test
    @DisplayName("测试delete方法 - 成功删除播报计划")
    void testDelete_ValidIds_ShouldSucceed() {
        // Given
        BroadcastPlanAddDto dto = new BroadcastPlanAddDto();
        dto.setIds(Arrays.asList(1L, 2L));

        // When
        Result result = broadcastPlanService.delete(dto);

        // Then
        assertNotNull(result);
        assertEquals(10000, result.getCode());
        verify(broadcastPlanRepository, times(1)).deleteAllById(Arrays.asList(1L, 2L));
        verify(broadcastPlanVoiceWorkRepository, times(1)).deleteAllByPlanId(Arrays.asList(1L, 2L));
    }

    @Test
    @DisplayName("测试delete方法 - 参数为空时返回失败")
    void testDelete_NullDto_ShouldReturnFailure() {
        // When
        Result result = broadcastPlanService.delete(null);

        // Then
        assertNotNull(result);
        assertEquals(10001, result.getCode());
        assertEquals("数据不能为空", result.getMessage());
    }

    @Test
    @DisplayName("测试delete方法 - ID列表为空时返回失败")
    void testDelete_EmptyIds_ShouldReturnFailure() {
        // Given
        BroadcastPlanAddDto dto = new BroadcastPlanAddDto();
        dto.setIds(new ArrayList<>());

        // When
        Result result = broadcastPlanService.delete(dto);

        // Then
        assertNotNull(result);
        assertEquals(10001, result.getCode());
        assertEquals("播报计划id不能为空", result.getMessage());
    }

    @Test
    @DisplayName("测试delete方法 - ID列表为null时返回失败")
    void testDelete_NullIds_ShouldReturnFailure() {
        // Given
        BroadcastPlanAddDto dto = new BroadcastPlanAddDto();
        dto.setIds(null);

        // When
        Result result = broadcastPlanService.delete(dto);

        // Then
        assertNotNull(result);
        assertEquals(10001, result.getCode());
        assertEquals("播报计划id不能为空", result.getMessage());
    }

    @Test
    @DisplayName("测试getInfo方法 - 成功获取播报计划详情")
    void testGetInfo_ValidId_ShouldReturnPlanInfo() {
        // Given
        Long planId = 1L;
        BroadcastPlan mockPlan = createMockBroadcastPlan();
        List<Map<String, Object>> mockVoiceWorkList = createMockVoiceWorkList();

        when(broadcastPlanRepository.findById(planId)).thenReturn(Optional.of(mockPlan));
        when(broadcastPlanVoiceWorkRepository.getByPlanId(planId)).thenReturn(mockVoiceWorkList);

        // When
        Result result = broadcastPlanService.getInfo(planId);

        // Then
        assertNotNull(result);
        assertEquals(10000, result.getCode());
        assertNotNull(result.getData());
        assertTrue(result.getData() instanceof BroadcastPlanInfoVo);

        BroadcastPlanInfoVo infoVo = (BroadcastPlanInfoVo) result.getData();
        assertEquals(mockPlan.getId(), infoVo.getId());
        assertEquals(mockVoiceWorkList, infoVo.getVoiceWorkRefList());
    }

    @Test
    @DisplayName("测试getInfo方法 - 播报计划不存在时返回失败")
    void testGetInfo_PlanNotFound_ShouldReturnFailure() {
        // Given
        Long planId = 999L;
        when(broadcastPlanRepository.findById(planId)).thenReturn(Optional.empty());

        // When
        Result result = broadcastPlanService.getInfo(planId);

        // Then
        assertNotNull(result);
        assertEquals(10001, result.getCode());
        assertEquals("播报计划不存在", result.getMessage());
    }

    @Test
    @DisplayName("测试addOrUpdate方法 - 更新不存在的计划时应该处理")
    void testAddOrUpdate_UpdateNonExistentPlan_ShouldHandleGracefully() {
        // Given
        BroadcastPlanAddDto dto = createValidBroadcastPlanAddDto();
        dto.setId(999L); // 不存在的ID

        try (MockedStatic<SecurityUtils> mockedSecurityUtils = mockStatic(SecurityUtils.class)) {
            mockedSecurityUtils.when(SecurityUtils::getLoginUser).thenReturn(mockLoginUser);
            when(broadcastPlanRepository.findById(999L)).thenReturn(Optional.empty());

            // When
            Result result = broadcastPlanService.addOrUpdate(dto);

            // Then
            assertNotNull(result);
            assertEquals(10000, result.getCode());
            // 验证没有调用删除和保存操作，因为计划不存在
            verify(broadcastPlanVoiceWorkRepository, never()).deleteByPlanId(anyLong());
            verify(broadcastPlanRepository, never()).save(any(BroadcastPlan.class));
        }
    }

    /**
     * 创建模拟的BroadcastPlan对象
     */
    private BroadcastPlan createMockBroadcastPlan() {
        BroadcastPlan plan = new BroadcastPlan();
        plan.setId(1L);
        plan.setStartTime("09:00");
        plan.setEndTime("18:00");
        plan.setShopId(1L);
        plan.setDeviceIds("1,2");
        plan.setType("1");
        plan.setIntervalTime("30");
        plan.setStartDate("2024-01-01");
        plan.setEndDate("2024-12-31");
        plan.setCreateTime(new Date());
        plan.setCreateUserId(1L);
        return plan;
    }

    /**
     * 创建模拟的语音作品列表
     */
    private List<Map<String, Object>> createMockVoiceWorkList() {
        List<Map<String, Object>> list = new ArrayList<>();
        Map<String, Object> work1 = new HashMap<>();
        work1.put("id", 1L);
        work1.put("vId", 1L);
        work1.put("name", "语音作品1");
        work1.put("sort", 1);
        list.add(work1);

        Map<String, Object> work2 = new HashMap<>();
        work2.put("id", 2L);
        work2.put("vId", 2L);
        work2.put("name", "语音作品2");
        work2.put("sort", 2);
        list.add(work2);

        return list;
    }

    /**
     * 创建有效的BroadcastPlanAddDto对象
     */
    private BroadcastPlanAddDto createValidBroadcastPlanAddDto() {
        BroadcastPlanAddDto dto = new BroadcastPlanAddDto();
        dto.setStartTime("09:00");
        dto.setEndTime("18:00");
        dto.setShopId(1L);
        dto.setDeviceIds(Arrays.asList(1L, 2L));
        dto.setVoiceWorkList(Arrays.asList(1L, 2L));
        dto.setType("1");
        dto.setIntervalTime("30");
        dto.setStartDate("2024-01-01");
        dto.setEndDate("2024-12-31");
        return dto;
    }

    /**
     * 创建模拟的BroadcastPlanVo列表
     */
    private List<BroadcastPlanVo> createMockBroadcastPlanVoList() {
        List<BroadcastPlanVo> list = new ArrayList<>();
        BroadcastPlanVo vo = new BroadcastPlanVo();
        vo.setId(1L);
        vo.setStartTime("09:00");
        vo.setEndTime("18:00");
        vo.setShopName("测试门店");
        list.add(vo);
        return list;
    }
}
